# East Baton Rouge Parish GIS Dataset Merger

## Overview

This project merges three related CSV files from the East Baton Rouge Parish GIS dataset into a comprehensive master file for parcel analysis and development opportunity identification.

## Source Datasets

### 1. Property_Information_20250716.csv (221,465 records, 50 columns)
- **Primary parcel data** with addresses, zoning districts, and Future Land Use categories
- **Key columns**: FULL ADDRESS, FUTURE LAND USE, ZONING DISTRICT, DESIGN LEVEL
- **Primary keys**: ADDRESS POINT ID, LOT ID

### 2. Lot_20250716.csv (197,236 records, 34 columns)
- **Lot boundaries** and character area classifications  
- **Key columns**: DESIGN LEVEL, ZONING TYPE, AREA (ACREAGE)
- **Primary key**: LOT ID

### 3. Building_Footprint_20250716.csv (197,368 records, 10 columns)
- **Building inventory** with construction details
- **Key columns**: YEAR BUILT, AREA IN SQUARE FEET, NUMBER OF FLOORS, HEIGHT (FEET)
- **Primary keys**: ID (Building ID), ID 2 (ADDRESS POINT ID)

## Merge Strategy

### Key Relationships
- **Property ↔ Lot**: Join on `LOT ID`
- **Property ↔ Building**: Join on `ADDRESS POINT ID` (Property) = `ID 2` (Building)

### Merge Process
1. **Base Dataset**: Property_Information (221,465 records)
2. **Left Join with Lot**: 99.99% match rate (221,446/221,464)
3. **Left Join with Building**: 79.8% match rate (176,702/221,464)
4. **Building Aggregation**: Multiple buildings per address aggregated into summary statistics

## Output Files

### 1. merged_ebr_parcels.csv (163MB, 221,464 records, 81 columns)
**Main merged dataset** with one row per property/address point containing:
- All property information (address, zoning, FLU)
- Lot characteristics (acreage, design level)
- Aggregated building data (count, total area, age statistics)

### 2. detailed_buildings_ebr.csv (93MB, 224,719 records)
**Detailed building export** with one row per building containing:
- Individual building characteristics
- Associated property and lot information
- Useful for building-level analysis

### 3. merged_ebr_parcels_sample.csv (762KB, 1,000 records)
**Sample file** with first 1,000 records for quick review and testing

## Key Merged Columns

### Property Information
- `ADDRESS POINT ID`, `LOT ID`, `FULL ADDRESS`
- `FUTURE LAND USE`, `ZONING DISTRICT`, `DESIGN LEVEL`
- `PLANNING DISTRICT NO`, `COUNCIL DISTRICT NO`

### Lot Information (prefixed with LOT_)
- `AREA (ACREAGE)` - Lot size in acres
- `LOT_DESIGN_LEVEL`, `LOT_ZONING_TYPE`

### Aggregated Building Data
- `BUILDING_COUNT` - Number of buildings per address
- `TOTAL_BUILDING_SQFT` - Sum of all building areas
- `OLDEST_BUILDING_YEAR`, `NEWEST_BUILDING_YEAR`
- `AVG_BUILDING_YEAR`, `AVG_BUILDING_SQFT`
- `MAX_FLOORS`, `MAX_HEIGHT_FEET`

## Usage Examples

### 1. Development Opportunity Identification
```python
import pandas as pd

# Load merged data
df = pd.read_csv('merged_ebr_parcels.csv')

# Find vacant land >1 acre
vacant_land = (df['BUILDING_COUNT'].fillna(0) == 0) & (df['AREA (ACREAGE)'] > 1)
print(f"Large vacant parcels: {vacant_land.sum():,}")

# Find redevelopment candidates (high-density FLU, low building density)
high_density_flu = df['FUTURE LAND USE'].isin(['DC', 'UN', 'MU', 'EC'])
low_density = (df['TOTAL_BUILDING_SQFT'].fillna(0) / df['AREA (ACREAGE)'].fillna(1)) < 5000
candidates = high_density_flu & low_density & (df['AREA (ACREAGE)'] > 0.25)
```

### 2. Zoning Compliance Analysis
```python
# Check zoning-FLU compatibility
compatibility = df.groupby(['FUTURE LAND USE', 'ZONING DISTRICT']).size()
print(compatibility.sort_values(ascending=False).head(10))
```

### 3. Financial Analysis Setup
```python
# Properties with building data for improvement-to-land value analysis
has_buildings = df['BUILDING_COUNT'].fillna(0) > 0
analysis_ready = df[has_buildings & df['AREA (ACREAGE)'].notna()]
```

## Merge Statistics

- **Final Records**: 221,464 (100% of property records preserved)
- **Property-Lot Match**: 99.99% (221,446/221,464)
- **Property-Building Match**: 79.8% (176,702/221,464)
- **Data Completeness**:
  - FULL ADDRESS: 100.0%
  - FUTURE LAND USE: 91.0%
  - ZONING DISTRICT: 73.1%
  - AREA (ACREAGE): 100.0%

## Key Insights

### Property Distribution
- **Total Properties**: 221,464
- **Total Acreage**: 347,726 acres
- **Average Parcel Size**: 1.57 acres
- **Properties with Buildings**: 79.8%
- **Vacant Properties**: 44,762 (20.2%)

### Development Opportunities
- **Large Vacant Parcels (>1 acre)**: 7,798
- **Redevelopment Candidates**: 8,495 properties in high-density FLU areas with low building density
- **Large Buildings (>50,000 sq ft)**: 411 properties

### Top Zoning-FLU Combinations
1. **RN + A1**: 36,340 properties (22.4%)
2. **RN + A2**: 19,170 properties (11.8%)
3. **CN + A3.1**: 14,341 properties (8.9%)

## Files and Scripts

- `merge_ebr_datasets.py` - Main merge script
- `analyze_merged_data.py` - Analysis and reporting script
- `requirements.txt` - Python dependencies
- `merge_report.txt` - Detailed merge statistics
- `merge_log.txt` - Processing log

## Requirements

```bash
pip install pandas numpy
```

## Running the Merge

```bash
python3 merge_ebr_datasets.py
```

## Running Analysis

```bash
python3 analyze_merged_data.py
```

This merged dataset enables comprehensive analysis for real estate development, zoning compliance, and market research in East Baton Rouge Parish.
