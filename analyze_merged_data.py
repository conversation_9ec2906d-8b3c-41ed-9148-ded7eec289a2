#!/usr/bin/env python3
"""
Analysis script for merged East Baton Rouge Parish dataset

This script demonstrates how to use the merged dataset for:
1. Development opportunity identification
2. Zoning-FLU compatibility analysis
3. Building density analysis
4. Geographic distribution analysis

Author: GPC Zoning Code Master
Date: 2025-07-25
"""

import pandas as pd
import numpy as np
from pathlib import Path

def load_merged_data(file_path="merged_ebr_parcels.csv"):
    """Load the merged dataset."""
    print(f"Loading merged dataset from {file_path}...")
    df = pd.read_csv(file_path, low_memory=False)
    print(f"Loaded {len(df):,} records with {len(df.columns)} columns")
    return df

def analyze_building_density(df):
    """Analyze building density patterns."""
    print("\n" + "="*60)
    print("BUILDING DENSITY ANALYSIS")
    print("="*60)
    
    # Properties with buildings
    has_buildings = df['BUILDING_COUNT'].fillna(0) > 0
    building_rate = (has_buildings.sum() / len(df)) * 100
    
    print(f"Properties with buildings: {has_buildings.sum():,} ({building_rate:.1f}%)")
    print(f"Properties without buildings: {(~has_buildings).sum():,} ({100-building_rate:.1f}%)")
    
    # Building statistics for properties with buildings
    if has_buildings.any():
        building_data = df[has_buildings]
        print(f"\nBuilding Statistics (for properties with buildings):")
        print(f"Average total building area: {building_data['TOTAL_BUILDING_SQFT'].mean():,.0f} sq ft")
        print(f"Median total building area: {building_data['TOTAL_BUILDING_SQFT'].median():,.0f} sq ft")
        print(f"Average building age: {building_data['AVG_BUILDING_YEAR'].mean():.0f}")
        
        # Identify large buildings
        large_buildings = building_data['LARGEST_BUILDING_SQFT'] > 50000
        print(f"Properties with buildings >50,000 sq ft: {large_buildings.sum():,}")

def analyze_zoning_flu_compatibility(df):
    """Analyze zoning district and Future Land Use compatibility."""
    print("\n" + "="*60)
    print("ZONING-FUTURE LAND USE COMPATIBILITY ANALYSIS")
    print("="*60)
    
    # Filter records with both zoning and FLU data
    complete_data = df.dropna(subset=['ZONING DISTRICT', 'FUTURE LAND USE'])
    print(f"Records with both zoning and FLU data: {len(complete_data):,}")
    
    # Create compatibility matrix
    compatibility_matrix = complete_data.groupby(['FUTURE LAND USE', 'ZONING DISTRICT']).size().unstack(fill_value=0)
    
    print(f"\nTop 10 FLU-Zoning combinations:")
    flu_zoning_combos = complete_data.groupby(['FUTURE LAND USE', 'ZONING DISTRICT']).size().sort_values(ascending=False).head(10)
    for (flu, zoning), count in flu_zoning_combos.items():
        pct = (count / len(complete_data)) * 100
        print(f"  {flu:10} + {zoning:10}: {count:6,} ({pct:4.1f}%)")

def identify_development_opportunities(df):
    """Identify potential development opportunities."""
    print("\n" + "="*60)
    print("DEVELOPMENT OPPORTUNITY IDENTIFICATION")
    print("="*60)
    
    # Properties without buildings (vacant land)
    vacant_land = df['BUILDING_COUNT'].fillna(0) == 0
    print(f"Vacant properties: {vacant_land.sum():,}")
    
    # Large vacant parcels (>1 acre)
    large_vacant = vacant_land & (df['AREA (ACREAGE)'].fillna(0) > 1)
    print(f"Large vacant parcels (>1 acre): {large_vacant.sum():,}")
    
    # Properties with old buildings (pre-1980)
    old_buildings = df['OLDEST_BUILDING_YEAR'].fillna(2025) < 1980
    print(f"Properties with buildings built before 1980: {old_buildings.sum():,}")
    
    # Properties in high-density FLU categories but with low building density
    high_density_flu = df['FUTURE LAND USE'].isin(['DC', 'UN', 'MU', 'EC'])
    low_building_density = (df['TOTAL_BUILDING_SQFT'].fillna(0) / df['AREA (ACREAGE)'].fillna(1)) < 5000  # <5000 sq ft per acre
    
    redevelopment_candidates = high_density_flu & low_building_density & (df['AREA (ACREAGE)'].fillna(0) > 0.25)
    print(f"Potential redevelopment candidates (high-density FLU, low building density): {redevelopment_candidates.sum():,}")

def analyze_geographic_distribution(df):
    """Analyze geographic distribution patterns."""
    print("\n" + "="*60)
    print("GEOGRAPHIC DISTRIBUTION ANALYSIS")
    print("="*60)
    
    # Planning district distribution
    if 'PLANNING DISTRICT NO' in df.columns:
        planning_dist = df['PLANNING DISTRICT NO'].value_counts().head(10)
        print("Top 10 Planning Districts by property count:")
        for district, count in planning_dist.items():
            pct = (count / len(df)) * 100
            print(f"  District {district:2}: {count:6,} ({pct:4.1f}%)")
    
    # Council district distribution
    if 'COUNCIL DISTRICT NO' in df.columns:
        council_dist = df['COUNCIL DISTRICT NO'].value_counts().head(10)
        print(f"\nTop 10 Council Districts by property count:")
        for district, count in council_dist.items():
            pct = (count / len(df)) * 100
            print(f"  District {district:2}: {count:6,} ({pct:4.1f}%)")

def generate_summary_statistics(df):
    """Generate overall summary statistics."""
    print("\n" + "="*60)
    print("SUMMARY STATISTICS")
    print("="*60)
    
    print(f"Total properties: {len(df):,}")
    print(f"Total columns: {len(df.columns)}")
    
    # Data completeness
    key_fields = ['FULL ADDRESS', 'FUTURE LAND USE', 'ZONING DISTRICT', 'AREA (ACREAGE)']
    print(f"\nData Completeness:")
    for field in key_fields:
        if field in df.columns:
            completeness = (df[field].notna().sum() / len(df)) * 100
            print(f"  {field:20}: {completeness:5.1f}%")
    
    # Acreage statistics
    if 'AREA (ACREAGE)' in df.columns:
        acreage_data = df['AREA (ACREAGE)'].dropna()
        print(f"\nProperty Size Distribution:")
        print(f"  Total acreage: {acreage_data.sum():,.0f} acres")
        print(f"  Average parcel size: {acreage_data.mean():.2f} acres")
        print(f"  Median parcel size: {acreage_data.median():.2f} acres")
        print(f"  Largest parcel: {acreage_data.max():.1f} acres")

def main():
    """Main analysis function."""
    print("East Baton Rouge Parish Merged Dataset Analysis")
    print("=" * 60)
    
    # Load data
    df = load_merged_data()
    
    # Run analyses
    generate_summary_statistics(df)
    analyze_building_density(df)
    analyze_zoning_flu_compatibility(df)
    identify_development_opportunities(df)
    analyze_geographic_distribution(df)
    
    print(f"\n{'='*60}")
    print("Analysis complete!")
    print("Use this merged dataset for:")
    print("- Financial analysis (improvement-to-land value ratios)")
    print("- Zoning compliance checks")
    print("- Development opportunity identification")
    print("- Market analysis and feasibility studies")

if __name__ == "__main__":
    main()
