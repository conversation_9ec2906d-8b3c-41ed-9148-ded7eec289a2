#!/usr/bin/env python3
"""
East Baton Rouge Parish GIS Dataset Merger

This script merges three related CSV files from the EBR GIS dataset:
1. Property_Information_20250716.csv - Primary parcel data
2. Lot_20250716.csv - Lot boundaries and character areas  
3. Building_Footprint_20250716.csv - Building inventory

Author: GPC Zoning Code Master
Date: 2025-07-25
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('merge_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress pandas warnings for cleaner output
warnings.filterwarnings('ignore', category=pd.errors.DtypeWarning)

class EBRDatasetMerger:
    """Handles merging of East Baton Rouge Parish GIS datasets."""
    
    def __init__(self, data_dir="CSV"):
        self.data_dir = Path(data_dir)
        self.property_file = self.data_dir / "Property_Information_20250716.csv"
        self.lot_file = self.data_dir / "Lot_20250716.csv"
        self.building_file = self.data_dir / "Building_Footprint_20250716.csv"
        
        # Initialize dataframes
        self.property_df = None
        self.lot_df = None
        self.building_df = None
        self.merged_df = None
        
        # Merge statistics
        self.merge_stats = {}
        
    def load_datasets(self):
        """Load all three CSV files with appropriate data types."""
        logger.info("Loading datasets...")
        
        try:
            # Load Property Information (base dataset)
            logger.info(f"Loading {self.property_file}")
            self.property_df = pd.read_csv(self.property_file, low_memory=False)
            logger.info(f"Property data: {len(self.property_df):,} records, {len(self.property_df.columns)} columns")
            
            # Load Lot data
            logger.info(f"Loading {self.lot_file}")
            self.lot_df = pd.read_csv(self.lot_file, low_memory=False)
            logger.info(f"Lot data: {len(self.lot_df):,} records, {len(self.lot_df.columns)} columns")
            
            # Load Building Footprint data
            logger.info(f"Loading {self.building_file}")
            self.building_df = pd.read_csv(self.building_file, low_memory=False)
            logger.info(f"Building data: {len(self.building_df):,} records, {len(self.building_df.columns)} columns")
            
            # Store initial counts
            self.merge_stats['property_records'] = len(self.property_df)
            self.merge_stats['lot_records'] = len(self.lot_df)
            self.merge_stats['building_records'] = len(self.building_df)
            
        except Exception as e:
            logger.error(f"Error loading datasets: {e}")
            raise
    
    def clean_and_validate_data(self):
        """Clean and validate the loaded datasets."""
        logger.info("Cleaning and validating data...")
        
        # Clean Property data
        if self.property_df is not None:
            # Convert key columns to appropriate types
            self.property_df['ADDRESS POINT ID'] = pd.to_numeric(self.property_df['ADDRESS POINT ID'], errors='coerce')
            self.property_df['LOT ID'] = pd.to_numeric(self.property_df['LOT ID'], errors='coerce')
            
            # Remove records with missing key identifiers
            initial_count = len(self.property_df)
            self.property_df = self.property_df.dropna(subset=['ADDRESS POINT ID', 'LOT ID'])
            removed_count = initial_count - len(self.property_df)
            if removed_count > 0:
                logger.warning(f"Removed {removed_count} property records with missing key identifiers")
        
        # Clean Lot data
        if self.lot_df is not None:
            self.lot_df['LOT ID'] = pd.to_numeric(self.lot_df['LOT ID'], errors='coerce')
            
            # Remove duplicates based on LOT ID
            initial_count = len(self.lot_df)
            self.lot_df = self.lot_df.drop_duplicates(subset=['LOT ID'])
            removed_count = initial_count - len(self.lot_df)
            if removed_count > 0:
                logger.warning(f"Removed {removed_count} duplicate lot records")
        
        # Clean Building data
        if self.building_df is not None:
            # Rename columns for clarity
            self.building_df = self.building_df.rename(columns={
                'ID': 'BUILDING_ID',
                'ID 2': 'ADDRESS_POINT_ID'
            })
            
            self.building_df['ADDRESS_POINT_ID'] = pd.to_numeric(self.building_df['ADDRESS_POINT_ID'], errors='coerce')
            
            # Convert numeric columns
            numeric_cols = ['YEAR BUILT', 'AREA IN SQUARE FEET', 'NUMBER OF FLOORS', 'HEIGHT (FEET)']
            for col in numeric_cols:
                if col in self.building_df.columns:
                    self.building_df[col] = pd.to_numeric(self.building_df[col], errors='coerce')
    
    def merge_datasets(self):
        """Perform the merge operations."""
        logger.info("Starting merge operations...")
        
        # Start with Property data as base
        merged = self.property_df.copy()
        logger.info(f"Base dataset: {len(merged):,} property records")
        
        # Merge with Lot data on LOT ID
        logger.info("Merging with lot data...")
        lot_cols_to_merge = [col for col in self.lot_df.columns if col not in merged.columns or col == 'LOT ID']
        lot_subset = self.lot_df[lot_cols_to_merge].copy()

        # Rename conflicting columns in lot data
        rename_dict = {}
        if 'DESIGN LEVEL' in lot_subset.columns:
            rename_dict['DESIGN LEVEL'] = 'LOT_DESIGN_LEVEL'
        if 'ZONING TYPE' in lot_subset.columns:
            rename_dict['ZONING TYPE'] = 'LOT_ZONING_TYPE'

        if rename_dict:
            lot_subset = lot_subset.rename(columns=rename_dict)

        merged = merged.merge(lot_subset, on='LOT ID', how='left', suffixes=('', '_LOT'))

        # Track merge statistics - use a column that should exist after merge
        lot_indicator_col = 'LOT_DESIGN_LEVEL' if 'LOT_DESIGN_LEVEL' in merged.columns else 'AREA (ACREAGE)'
        if lot_indicator_col in merged.columns:
            lot_matches = merged[lot_indicator_col].notna().sum()
        else:
            # Fallback: count non-null values in any lot-specific column
            lot_specific_cols = [col for col in merged.columns if col.startswith('LOT_') or col in ['AREA (ACREAGE)', 'PLANNING DISTRICT']]
            if lot_specific_cols:
                lot_matches = merged[lot_specific_cols[0]].notna().sum()
            else:
                lot_matches = 0

        self.merge_stats['property_lot_matches'] = lot_matches
        self.merge_stats['property_lot_no_match'] = len(merged) - lot_matches
        
        logger.info(f"Lot merge: {lot_matches:,} matches, {len(merged) - lot_matches:,} no match")
        
        # Merge with Building data on ADDRESS POINT ID
        logger.info("Merging with building data...")
        building_subset = self.building_df.copy()
        
        # Create aggregated building data per address point
        building_agg = self._aggregate_building_data(building_subset)
        
        merged = merged.merge(building_agg, left_on='ADDRESS POINT ID', right_on='ADDRESS_POINT_ID', how='left')
        
        # Track building merge statistics
        building_matches = merged['BUILDING_COUNT'].notna().sum()
        self.merge_stats['property_building_matches'] = building_matches
        self.merge_stats['property_building_no_match'] = len(merged) - building_matches
        
        logger.info(f"Building merge: {building_matches:,} matches, {len(merged) - building_matches:,} no match")
        
        self.merged_df = merged
        self.merge_stats['final_record_count'] = len(merged)
        
        logger.info(f"Merge complete: {len(merged):,} final records")
    
    def _aggregate_building_data(self, building_df):
        """Aggregate building data by ADDRESS_POINT_ID."""
        logger.info("Aggregating building data by address point...")
        
        # Group by ADDRESS_POINT_ID and aggregate
        agg_functions = {
            'BUILDING_ID': 'count',  # Count of buildings
            'YEAR BUILT': ['min', 'max', 'mean'],  # Oldest, newest, average year built
            'AREA IN SQUARE FEET': ['sum', 'mean', 'max'],  # Total, average, largest building area
            'NUMBER OF FLOORS': ['sum', 'mean', 'max'],  # Total, average, max floors
            'HEIGHT (FEET)': ['mean', 'max']  # Average and max height
        }
        
        building_agg = building_df.groupby('ADDRESS_POINT_ID').agg(agg_functions).reset_index()
        
        # Flatten column names
        building_agg.columns = [
            'ADDRESS_POINT_ID',
            'BUILDING_COUNT',
            'OLDEST_BUILDING_YEAR',
            'NEWEST_BUILDING_YEAR', 
            'AVG_BUILDING_YEAR',
            'TOTAL_BUILDING_SQFT',
            'AVG_BUILDING_SQFT',
            'LARGEST_BUILDING_SQFT',
            'TOTAL_FLOORS',
            'AVG_FLOORS',
            'MAX_FLOORS',
            'AVG_HEIGHT_FEET',
            'MAX_HEIGHT_FEET'
        ]
        
        # Fill NaN values with 0 for building count where no buildings exist
        building_agg['BUILDING_COUNT'] = building_agg['BUILDING_COUNT'].fillna(0)
        
        logger.info(f"Aggregated building data for {len(building_agg):,} unique address points")
        
        return building_agg

    def generate_merge_report(self):
        """Generate comprehensive merge statistics and data quality report."""
        logger.info("Generating merge report...")

        report = []
        report.append("=" * 80)
        report.append("EAST BATON ROUGE PARISH GIS DATASET MERGE REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Input dataset statistics
        report.append("INPUT DATASETS:")
        report.append("-" * 40)
        report.append(f"Property Information: {self.merge_stats['property_records']:,} records")
        report.append(f"Lot Data:            {self.merge_stats['lot_records']:,} records")
        report.append(f"Building Footprint:  {self.merge_stats['building_records']:,} records")
        report.append("")

        # Merge statistics
        report.append("MERGE RESULTS:")
        report.append("-" * 40)
        report.append(f"Final merged records: {self.merge_stats['final_record_count']:,}")
        report.append("")

        # Property-Lot merge
        prop_lot_match_rate = (self.merge_stats['property_lot_matches'] /
                              self.merge_stats['property_records']) * 100
        report.append(f"Property-Lot Merge:")
        report.append(f"  Matched:    {self.merge_stats['property_lot_matches']:,} ({prop_lot_match_rate:.1f}%)")
        report.append(f"  No match:   {self.merge_stats['property_lot_no_match']:,}")
        report.append("")

        # Property-Building merge
        prop_bldg_match_rate = (self.merge_stats['property_building_matches'] /
                               self.merge_stats['property_records']) * 100
        report.append(f"Property-Building Merge:")
        report.append(f"  Matched:    {self.merge_stats['property_building_matches']:,} ({prop_bldg_match_rate:.1f}%)")
        report.append(f"  No match:   {self.merge_stats['property_building_no_match']:,}")
        report.append("")

        # Data quality analysis
        if self.merged_df is not None:
            report.append("DATA QUALITY ANALYSIS:")
            report.append("-" * 40)

            # Key field completeness
            key_fields = ['FULL ADDRESS', 'FUTURE LAND USE', 'ZONING DISTRICT', 'LOT_DESIGN_LEVEL']
            for field in key_fields:
                if field in self.merged_df.columns:
                    completeness = (self.merged_df[field].notna().sum() / len(self.merged_df)) * 100
                    report.append(f"{field:20}: {completeness:5.1f}% complete")

            report.append("")

            # Building statistics
            has_buildings = self.merged_df['BUILDING_COUNT'].fillna(0) > 0
            building_rate = (has_buildings.sum() / len(self.merged_df)) * 100
            report.append(f"Properties with buildings: {has_buildings.sum():,} ({building_rate:.1f}%)")

            if has_buildings.any():
                avg_buildings = self.merged_df.loc[has_buildings, 'BUILDING_COUNT'].mean()
                max_buildings = self.merged_df['BUILDING_COUNT'].max()
                report.append(f"Average buildings per property: {avg_buildings:.1f}")
                report.append(f"Maximum buildings per property: {max_buildings:.0f}")

            report.append("")

            # Zoning district distribution
            if 'ZONING DISTRICT' in self.merged_df.columns:
                zoning_counts = self.merged_df['ZONING DISTRICT'].value_counts().head(10)
                report.append("TOP 10 ZONING DISTRICTS:")
                for zone, count in zoning_counts.items():
                    pct = (count / len(self.merged_df)) * 100
                    report.append(f"  {zone:15}: {count:6,} ({pct:4.1f}%)")

            report.append("")

            # Future Land Use distribution
            if 'FUTURE LAND USE' in self.merged_df.columns:
                flu_counts = self.merged_df['FUTURE LAND USE'].value_counts().head(10)
                report.append("TOP 10 FUTURE LAND USE CATEGORIES:")
                for flu, count in flu_counts.items():
                    pct = (count / len(self.merged_df)) * 100
                    report.append(f"  {flu:15}: {count:6,} ({pct:4.1f}%)")

        report.append("")
        report.append("=" * 80)

        # Write report to file
        report_text = "\n".join(report)
        with open('merge_report.txt', 'w') as f:
            f.write(report_text)

        # Also log to console
        logger.info("Merge report generated:")
        print("\n" + report_text)

        return report_text

    def export_merged_data(self, output_file="merged_ebr_parcels.csv"):
        """Export the merged dataset to CSV."""
        if self.merged_df is None:
            logger.error("No merged data to export. Run merge_datasets() first.")
            return

        logger.info(f"Exporting merged data to {output_file}...")

        try:
            self.merged_df.to_csv(output_file, index=False)
            logger.info(f"Successfully exported {len(self.merged_df):,} records to {output_file}")

            # Also create a sample file with first 1000 records for quick review
            sample_file = output_file.replace('.csv', '_sample.csv')
            self.merged_df.head(1000).to_csv(sample_file, index=False)
            logger.info(f"Sample file created: {sample_file} (first 1,000 records)")

        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            raise

    def create_detailed_building_export(self, output_file="detailed_buildings_ebr.csv"):
        """Create a detailed export with one row per building."""
        if self.building_df is None or self.merged_df is None:
            logger.error("Building data or merged data not available.")
            return

        logger.info("Creating detailed building export...")

        # Merge building details with property information
        detailed = self.building_df.merge(
            self.property_df[['ADDRESS POINT ID', 'FULL ADDRESS', 'FUTURE LAND USE', 'ZONING DISTRICT']],
            left_on='ADDRESS_POINT_ID',
            right_on='ADDRESS POINT ID',
            how='left'
        )

        # Add lot information
        detailed = detailed.merge(
            self.lot_df[['LOT ID', 'DESIGN LEVEL', 'AREA (ACREAGE)']].rename(columns={
                'DESIGN LEVEL': 'LOT_DESIGN_LEVEL',
                'AREA (ACREAGE)': 'LOT_ACREAGE'
            }),
            left_on='ADDRESS POINT ID',
            right_on='LOT ID',
            how='left'
        )

        detailed.to_csv(output_file, index=False)
        logger.info(f"Detailed building export created: {output_file} ({len(detailed):,} records)")

    def run_complete_merge(self):
        """Execute the complete merge process."""
        logger.info("Starting complete merge process...")

        try:
            self.load_datasets()
            self.clean_and_validate_data()
            self.merge_datasets()
            self.generate_merge_report()
            self.export_merged_data()
            self.create_detailed_building_export()

            logger.info("Merge process completed successfully!")

        except Exception as e:
            logger.error(f"Merge process failed: {e}")
            raise


def main():
    """Main execution function."""
    print("East Baton Rouge Parish GIS Dataset Merger")
    print("=" * 50)

    # Initialize merger
    merger = EBRDatasetMerger()

    # Run complete merge process
    merger.run_complete_merge()

    print("\nMerge completed! Check the following output files:")
    print("- merged_ebr_parcels.csv (main merged dataset)")
    print("- merged_ebr_parcels_sample.csv (first 1,000 records)")
    print("- detailed_buildings_ebr.csv (one row per building)")
    print("- merge_report.txt (detailed merge statistics)")
    print("- merge_log.txt (processing log)")


if __name__ == "__main__":
    main()
